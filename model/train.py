import json
import os
import platform
import re
import subprocess
import time
import multiprocessing
from collections import Counter
from concurrent.futures import ThreadPoolExecutor
from typing import List, Tuple, Dict

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader


def load_data(file_path: str) -> Tuple[List[str], List[int]]:
    """
    Loads messages and labels from a JSON file.

    Args:
        file_path: Path to the JSON file.

    Returns:
        Tuple of (messages, labels).
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    messages = [item['message'] for item in data]
    labels = [item['memory_type'] for item in data]
    return messages, labels


class SimpleTokenizer:
    """Tokenizer that converts text to fixed-length token sequences."""

    def __init__(self, max_len: int = 20):
        """
        Initialize tokenizer with max sequence length.

        Args:
            max_len: Maximum sequence length (default: 20).
        """
        self.max_len = max_len
        self.vocab = {}
        self.pad_token = 0

    def fit(self, texts: List[str]) -> None:
        """
        Build vocabulary from texts.

        Args:
            texts: List of text strings to build vocabulary.
        """
        all_words = ' '.join(texts).split()
        word_counts = Counter(all_words)
        self.vocab = {word: idx + 1 for idx,
                      word in enumerate(word_counts.keys())}

    def tokenize(self, text: str) -> List[int]:
        """
        Tokenize a single text into a fixed-length sequence.

        Args:
            text: Input text to tokenize.

        Returns:
            List of token IDs, padded or truncated to max_len.
        """
        words = text.split()
        token_ids = [self.vocab.get(word, self.pad_token) for word in words]
        if len(token_ids) > self.max_len:
            token_ids = token_ids[:self.max_len]
        else:
            token_ids += [self.pad_token] * (self.max_len - len(token_ids))
        return token_ids


class MemoryDataset(Dataset):
    """PyTorch dataset for memory classification."""

    def __init__(self, tokens: List[List[int]], labels: List[int]):
        """
        Initialize dataset with tokens and labels.
        
        Args:
            tokens: List of tokenized sequences.
            labels: List of corresponding labels.
        """
        self.tokens = torch.tensor(tokens, dtype=torch.long)
        self.labels = torch.tensor(labels, dtype=torch.long)

    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.labels)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get item by index.
        
        Args:
            idx: Index of the item.
        
        Returns:
            Tuple of (tokens, label).
        """
        return self.tokens[idx], self.labels[idx]


def create_dataloader(dataset: MemoryDataset, batch_size: int = 4) -> DataLoader:
    """
    Create a DataLoader for the dataset.
    
    Args:
        dataset: MemoryDataset instance.
        batch_size: Number of samples per batch (default: 4).
    
    Returns:
        DataLoader for batching data.
    """
    return DataLoader(dataset, batch_size=batch_size, shuffle=True)


class MemoryClassifierConfig:
    """Configuration for the memory classifier."""

    def __init__(self, vocab_size: int, embed_dim: int = 32, hidden_dim: int = 64, output_dim: int = 3):
        """
        Initialize configuration.
        
        Args:
            vocab_size: Size of the vocabulary.
            embed_dim: Embedding dimension (default: 32).
            hidden_dim: LSTM hidden dimension (default: 64).
            output_dim: Number of output classes (default: 3).
        """
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim

    @classmethod
    def default(cls, vocab_size: int) -> 'MemoryClassifierConfig':
        """
        Create default configuration.
        
        Args:
            vocab_size: Size of the vocabulary.
        
        Returns:
            MemoryClassifierConfig instance.
        """
        return cls(vocab_size)

    @classmethod
    def from_dict(cls, config_dict: Dict) -> 'MemoryClassifierConfig':
        """
        Create configuration from a dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters.
        
        Returns:
            MemoryClassifierConfig instance.
        """
        return MemoryClassifierConfig(**config_dict)

    def to_dict(self) -> Dict:
        """
        Convert configuration to a dictionary.
        
        Returns:
            Dictionary containing configuration parameters.
        """
        return self.__dict__


class MemoryClassifier(nn.Module):
    """Neural network for memory type classification."""

    def __init__(self, config: MemoryClassifierConfig):
        """
        Initialize the classifier model.
        
        Args:
            vocab_size: Size of the vocabulary.
            embed_dim: Embedding dimension (default: 32).
            hidden_dim: LSTM hidden dimension (default: 64).
            output_dim: Number of output classes (default: 3).
        """
        super(MemoryClassifier, self).__init__()
        self.config = config
        vocab_size = config.vocab_size
        embed_dim = config.embed_dim
        hidden_dim = config.hidden_dim
        output_dim = config.output_dim
        self.embedding = nn.Embedding(vocab_size + 1, embed_dim)  # +1 for padding
        self.lstm = nn.LSTM(embed_dim, hidden_dim, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the model.
        
        Args:
            x: Input tensor of token IDs.
        
        Returns:
            Output logits for classification.
        """
        x = self.embedding(x)
        _, (hn, _) = self.lstm(x)
        return self.fc(hn.squeeze(0))

    def save(self, path: str) -> None:
        """
        Save the model and config to a folder.
        
        Args:
            path: Folder path to save the model.
        """
        model_path = f"{path}/model.pt"
        config_path = f"{path}/config.json"
        torch.save(self.state_dict(), model_path)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config.to_dict(), f)


def train_model(model: nn.Module, dataloader: DataLoader, epochs: int = 50, lr: float = 0.001) -> None:
    """
    Train the model.
    
    Args:
        model: Neural network model to train.
        dataloader: DataLoader with training data.
        epochs: Number of training epochs (default: 50).
        lr: Learning rate (default: 0.001).
    """
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    criterion = nn.CrossEntropyLoss()

    for epoch in range(epochs):
        model.train()
        total_loss = 0
        for tokens, labels in dataloader:
            optimizer.zero_grad()
            outputs = model(tokens)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        if (epoch + 1) % 10 == 0:
            print(
                f"Epoch {epoch + 1}/{epochs}, Loss: {total_loss / len(dataloader):.4f}")


def predict_memory_type(
    text: str, tokenizer: SimpleTokenizer, model: nn.Module,
    device: str = 'cuda'
) -> int:
    """
    Predict memory type for a single text.
    
    Args:
        text: Input text to classify.
        tokenizer: Trained tokenizer.
        model: Trained model.
    
    Returns:
        Predicted label (0 = NO_MEMORY, 1 = SHORT_TERM_MEMORY, 2 = LONG_TERM_MEMORY).
    """
    model.eval()
    model.to(device)
    if device == 'cuda':
        tokens = tokenizer.tokenize(text)
        tokens_tensor = torch.tensor([tokens], dtype=torch.long).cuda()
    else:
        tokens = tokenizer.tokenize(text)
        tokens_tensor = torch.tensor([tokens], dtype=torch.long).cpu()

    with torch.no_grad():
        output = model(tokens_tensor)
        pred = torch.argmax(output, dim=1).item()
    return pred


def save_model(model: nn.Module, path: str) -> None:
    """
    Save the model and configuration to a folder.
    
    Args:
        model: Trained model.
        path: File path to save the model.
    """
    os.makedirs(path, exist_ok=True)
    model.save(path)


def save_tokenizer(tokenizer: SimpleTokenizer, path: str) -> None:
    """
    Save the tokenizer to a file.
    
    Args:
        tokenizer: Trained tokenizer.
        path: File path to save the tokenizer.
    """
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(tokenizer.vocab, f)


def load_model(
    path: str
) -> nn.Module:
    """
    Load the model from a folder containing the model and configuration.

    Args:
        path:

    Returns:
        Loaded model.
    """
    model_path = f"{path}/model.pt"
    config_path = f"{path}/config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = MemoryClassifierConfig.from_dict(json.load(f))
        model = MemoryClassifier(config)
        model.load_state_dict(torch.load(model_path))
        model.lstm.flatten_parameters()
    return model


def load_tokenizer(path: str) -> SimpleTokenizer:
    """
    Load the tokenizer from a file.
    
    Args:
        path: File path to load the tokenizer from.
    
    Returns:
        Loaded tokenizer.
    """
    with open(path, 'r', encoding='utf-8') as f:
        vocab = json.load(f)
        tokenizer = SimpleTokenizer()
        tokenizer.vocab = vocab
    return tokenizer


def present_test_cases(
    model: nn.Module,
    tokenizer: SimpleTokenizer,
) -> None:
    """
    Test the model on a set of messages and labels.
    
    Args:
        model: Trained model.
        tokenizer: Trained tokenizer.
        test_labels: List of corresponding labels.
    """
    messages = [
        "Hello, how are you?",
        "My name is John.",
        "I am a student.",
        "I love zoos!",
        "I saw a giraffe today.",
        "How is the weather today?",
        "What are your plans for the weekend?",
        "My mother's birthday is next week.",
        "I am going to the zoo tomorrow.",
        "I am going to the zoo next week.",
        "I am going to the zoo next month.",
    ]
    for message in messages:
        predicted_label = predict_memory_type(message, tokenizer, model)
        print(f"Predicted label for '{message}': {predicted_label}")
        print()


def _get_cpu_info():
    if platform.system() == "Windows":
        return platform.processor()
    elif platform.system() == "Darwin":
        os.environ['PATH'] = os.environ['PATH'] + os.pathsep + '/usr/sbin'
        command = "sysctl -n machdep.cpu.brand_string"
        return subprocess.check_output(command).strip()
    elif platform.system() == "Linux":
        command = "cat /proc/cpuinfo"
        all_info = subprocess.check_output(
            command, shell=True).decode().strip()
        for line in all_info.split("\n"):
            if "model name" in line:
                return re.sub(".*model name.*:", "", line, 1)
    return ""


def _get_cpu_cores():
    return multiprocessing.cpu_count()


def load_test(
    model: nn.Module,
    tokenizer: SimpleTokenizer,
    threads: int = 50,
    device: str = "cuda"
) -> None:
    """
    Load test.
    """
    model.to(device)
    model.eval()

    def predict_memory_type_thread(messages: List[str]) -> List[int]:
        nonlocal model, tokenizer
        results = []
        for message in messages:
            result = predict_memory_type(message, tokenizer, model, device)
            results.append(result)
        return results

    messages = [
        "Hello, how are you?",
        "My name is John.",
        "I am a student.",
        "I love zoos!",
        "I saw a giraffe today.",
        "How is the weather today?",
        "What are your plans for the weekend?",
        "My mother's birthday is next week.",
        "I am going to the zoo tomorrow.",
        "I am going to the zoo next week.",
        "I am going to the zoo next month.",
    ]
    # Push messages to the GPU
    start = time.time()
    futures = []
    with ThreadPoolExecutor(max_workers=threads) as executor:
        for _ in range(threads):
            future = executor.submit(predict_memory_type_thread, messages)
            futures.append(future)

    results = []
    for future in futures:
        results.extend(future.result())

    end = time.time()

    print("Test summary:")
    if device == "cuda":
        print(f"Device: {torch.cuda.get_device_name(0)}")
        multi_processor_count = torch.cuda.get_device_properties(
            0).multi_processor_count
        cores_per_processor = torch.cuda.get_device_properties(
            0).max_threads_per_multi_processor
        print(f"Total cores: {multi_processor_count * cores_per_processor}")
    elif device == "cpu":
        print(f"Device: {_get_cpu_info()}")
        print(f"Total cores: {_get_cpu_cores()}")
    print(f"Total parallel tasks: {threads}")
    print(f"Messages per task: {len(messages)}")
    print(f"Time per message: {(end - start) /
          (len(messages) * threads):.4f}s")
    print(f"Total time: {end - start:.4f}s")


def train(data_path: str, tokenizer_length: int = 20) -> Tuple[nn.Module, SimpleTokenizer]:
    """
        Train the model on a dataset.

        Args:
            data_path: Path to the JSON file containing the dataset.
            tokenizer_length: Maximum sequence length for the tokenizer (default: 20).

        Returns:
            Tuple of (trained model, trained tokenizer).
    """
    print("Training model...")
    messages, labels = load_data(data_path)
    tokenizer = SimpleTokenizer(max_len=int(tokenizer_length))
    tokenizer.fit(messages)
    tokenized_messages = [tokenizer.tokenize(msg) for msg in messages]
    dataset = MemoryDataset(tokenized_messages, labels)
    dataloader = create_dataloader(dataset, batch_size=4)
    model = MemoryClassifier(
        MemoryClassifierConfig.default(len(tokenizer.vocab))
    )
    train_model(model, dataloader, epochs=50)
    return model, tokenizer
