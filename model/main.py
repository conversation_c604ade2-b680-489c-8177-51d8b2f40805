from argparse import ArgumentParser, Namespace

from train import (
    train,
    load_model,
    load_tokenizer,
    save_model,
    save_tokenizer,
    present_test_cases,
    predict_memory_type,
    load_test
)



def create_arg_parser() -> Namespace:
    """Create an argument parser for command-line arguments."""
    parser = ArgumentParser(description="Train a memory classifier.")
    parser.add_argument("--train", "-t", action="store_true", help="If set, train the model.")
    parser.add_argument("--save", "-s", action="store_true", help="If set, save the model.")
    parser.add_argument("--present", "-p", action="store_true", help="If set, present test cases.")
    parser.add_argument("--data_path", "-d", help="Path to the data file.")
    parser.add_argument("--model_path", "-m", help="Path to save the model.")
    parser.add_argument("--tokenizer_path", "-k", help="Path to save the tokenizer.")
    parser.add_argument("--tokenizer_length", "-l", help="Length of the tokenizer.")
    parser.add_argument("--test", "-e", action="store_true", help="If set, test the model.")
    parser.add_argument("--load_test", "-o", action="store_true", help="If set, load the test data.")
    parser.add_argument("--load_test_device", "-u", help="Device to load the test data.")
    parser.add_argument("--load_test_threads", "-n", help="Number of threads to load the test data.")
    return parser


def main():
    """Main function to orchestrate data loading, training, and inference."""
    parser = create_arg_parser()
    args = parser.parse_args()
    if not any(vars(args).values()):
        parser.print_help()
        exit(0)
        
    model, tokenizer = None, None
    if args.train:
        if not any(
            [args.data_path, args.tokenizer_length]
        ):
            raise ValueError("Data path and tokenizer length are required for training.")
        model, tokenizer = train(args.data_path, args.tokenizer_length)
        if args.save:
            if not args.model_path:
                print("Using default model path.")
                args.model_path = "model"
            save_model(model, args.model_path)
            if not args.tokenizer_path:
                print("Using default tokenizer path.")
                args.tokenizer_path = "tokenizer.json"
            save_tokenizer(tokenizer, args.tokenizer_path)
            print("Model and tokenizer saved successfully.")

    if args.present:
        if not model:
            if not args.model_path:
                print("Using default model path.")
                args.model_path = "model"
            model = load_model(args.model_path)
        if not tokenizer:
            if not args.tokenizer_path:
                print("Using default tokenizer path.")
                args.tokenizer_path = "tokenizer.json"
            tokenizer = load_tokenizer(args.tokenizer_path)

        present_test_cases(model, tokenizer)

    if args.test:
        if not model:
            if not args.model_path:
                print("Using default model path.")
                args.model_path = "model"
            model = load_model(args.model_path)
        if not tokenizer:
            if not args.tokenizer_path:
                print("Using default tokenizer path.")
                args.tokenizer_path = "tokenizer.json"
            tokenizer = load_tokenizer(args.tokenizer_path)

        while True:
            text = input("Enter a text: ")
            memory_type = predict_memory_type(text, tokenizer, model)
            print(f"Memory type: {memory_type}")
            if input("Continue? (y/n): ") == "n":
                break

    if args.load_test:
        if not args.load_test_threads:
            print("Using default number of threads.")
            args.load_test_threads = 50
        if not args.load_test_device:
            print("Using default device.")
            args.load_test_device = "cuda"
        if not model:
            if not args.model_path:
                print("Using default model path.")
                args.model_path = "model"
            model = load_model(args.model_path)
        if not tokenizer:
            if not args.tokenizer_path:
                print("Using default tokenizer path.")
                args.tokenizer_path = "tokenizer.json"
            tokenizer = load_tokenizer(args.tokenizer_path)
        load_test(model, tokenizer, args.load_test_threads, args.load_test_device)


if __name__ == "__main__":
    main()
