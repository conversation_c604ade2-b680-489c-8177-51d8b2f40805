from enum import Enum
from typing import List, Dict

from instructor import from_openai
from openai import OpenAI
from pydantic import BaseModel


class MemoryType(Enum):
    """Enum for the memory types."""
    NO_MEMORY = 0
    SHORT_TERM_MEMORY = 1
    LONG_TERM_MEMORY = 2


class Classification(BaseModel):
    """Classification for a message."""
    index: int
    memory_type: MemoryType


class MemoryClassifierResponse(BaseModel):
    """Response for the memory classifier."""
    classifications: List[Classification]


def classify_messages(
    messages: List[str],
    batch_size: int = 10,
    model: str = "gpt-4o-mini",
) -> List[Dict[str, str]]:
    """
    Classifies the messages with an agent and returns a list of dictionaries
    containing each message and its memory classification.

    Args:
        messages: List of messages to classify.
        batch_size: Number of messages to process per batch.
        model: The model to use for classification (default: gpt-4o-mini).

    Returns:
        List of dictionaries, each with 'message' and 'memory_type' keys.
    """
    # Load the system prompt
    with open("prompts/classifier.txt", "r") as f:
        prompt = f.read()

    responses = []
    instructor = from_openai(OpenAI())

    for i in range(0, len(messages), batch_size):
        print(f"Processing batch {i // batch_size + 1}...")
        batch = messages[i:i + batch_size]
        # Create a user message with enumerated messages for clarity
        user_message = "\n".join(
            f"{idx}. {msg}" for idx, msg in enumerate(batch, start=i))

        messages_input = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_message},
        ]

        # Call the model with the instructor
        response: MemoryClassifierResponse = instructor.chat.completions.create(
            response_model=MemoryClassifierResponse,
            messages=messages_input,
            model=model,
        )

        # Map classifications back to original messages
        for classification in response.classifications:
            try:
                index = int(classification.index)
                memory_type= classification.memory_type
                # Convert string to MemoryType enum for validation
                if index < len(messages):  # Ensure index is valid
                    responses.append({
                        "message": messages[index],
                        "memory_type": memory_type.value
                    })
                else:
                    print(f"Warning: Invalid index {index} for message batch.")
            except (KeyError, ValueError, KeyError) as e:
                print(
                    f"Warning: Invalid classification format: {classification}, error: {e}"
                )

    return responses
