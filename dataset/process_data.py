import os
import json
import zstandard as zstd
from huggingface_hub import hf_hub_download


def download_dataset(
    download_train: bool = True,
    download_test: bool = False,
    download_validation: bool = False,
) -> None:
    """
    Downloads the dataset: agentlans/li2017dailydialog
    """
    # Define the repository and dataset details
    repo_id = "agentlans/li2017dailydialog"
    repo_type = "dataset"
    token = os.environ.get("HF_TOKEN")

    train_path, test_path, validation_path = None, None, None
    # Download train data
    if download_train:
        train_path = hf_hub_download(
            repo_id=repo_id,
            repo_type=repo_type,
            filename="train.jsonl.zst",
            token=token,
        )
    # Download test data
    if download_test:
        test_path = hf_hub_download(
            repo_id=repo_id,
            repo_type=repo_type,
            filename="test.jsonl.zst",
            token=token,
        )
    # Download validation data
    if download_validation:
        validation_path = hf_hub_download(
            repo_id=repo_id,
            repo_type=repo_type,
            filename="validation.jsonl.zst",
            token=token,
        )

    return train_path, test_path, validation_path


def load_jsonl_zst(file_path: str) -> list:
    """
    Loads a .jsonl.zst file and returns a list of JSON objects.
    """
    data = []
    with open(file_path, "rb") as f:
        # Decompress the .zst file
        dctx = zstd.ZstdDecompressor()
        with dctx.stream_reader(f) as reader:
            # Read lines from decompressed data
            decompressed_data = reader.read().decode("utf-8")
            # Process each line as a JSON object
            for line in decompressed_data.splitlines():
                if line.strip():  # Skip empty lines
                    data.append(json.loads(line))
    return data


def load_train_dataset() -> list:
    """
    Loads the train dataset: agentlans/li2017dailydialog
    """
    if os.path.exists("data/train.json"):
        with open("data/train.json", "r") as f:
            return json.load(f)
    train_path, _, _ = download_dataset(
        download_train=True, download_test=False, download_validation=False)
    if train_path:
        data = load_jsonl_zst(train_path)
        os.makedirs("data", exist_ok=True)
        with open("data/train.json", "w") as f:
            json.dump(data, f, indent=4)
        return data
    else:
        raise FileNotFoundError("Train dataset could not be downloaded.")


def get_human_messages(
    dataset: list,
) -> list:
    """
    Extracts the human messages from the dataset.
    """
    human_messages = []
    for dialogue in dataset:
        conversation = dialogue.get("conversations", [])
        for message in conversation:
            if message.get("from") == "human":
                human_messages.append(message["value"])
    print(f"Found {len(human_messages)} human messages.")
    return human_messages

