import json

from dotenv import load_dotenv

from process_data import load_train_dataset, get_human_messages
from classify import classify_messages

load_dotenv()


def main():
    data = load_train_dataset()
    # Print the structure of the first few entries
    print(f"Number of dialogues: {len(data)}")
    print("Example dialogue entry:", data[0])
    print("Keys in first dialogue:", data[0].keys())
    human_messages = get_human_messages(data)
    print(f"Number of human messages: {len(human_messages)}")
    print("Example human message:", human_messages[0])
    # Process 5% of the data
    human_messages_sample = human_messages[: int(len(data) * 0.05)]
    results = classify_messages(human_messages_sample)
    with open("results.json", "w") as f:
        json.dump(results, f)


if __name__ == "__main__":
    main()
