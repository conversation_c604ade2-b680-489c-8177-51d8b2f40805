services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongodb_data:/data/db
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
  flower:
    image: mher/flower:latest
    ports:
      - "5555:5555"
    environment:
      - FLOWER_BROKER=amqp://admin:password@rabbitmq:5672
      - FLOWER_BASIC_AUTH=admin:password
    depends_on:
      - rabbitmq
volumes:
  mongodb_data:
  rabbitmq_data:
