import os

from celery import Celery

CELERY_BROKER = os.getenv(
    "CELERY_BROKER_URL", "amqp://admin:password@localhost:5672"
)
celery_app = Celery(
    "core_tasks",
    broker=CELERY_BROKER,
    backend="rpc://"
)
celery_app.conf.update(
    task_serializer="json",
    result_serializer="json",
    timezone="America/Sao_Paulo"
)
celery_app.autodiscover_tasks(['src.workers.memory_classification'])
