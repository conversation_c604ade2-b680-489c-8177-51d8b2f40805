from datetime import datetime

from celery import shared_task

from src.models import (
    LongTermMemory,
    ShortTermMemory,
    Message
)
from src.repositories.long_term_memory import LongTermMemoryRepository
from src.repositories.short_term_memory import ShortTermMemoryRepository
from src.services.labeler import label_message


@shared_task(
    name="memory_classification.process_message",
    queue="memory:process"
)
def process_message(
    message_dict: dict
) -> dict:
    message = Message(**message_dict)
    label = label_message(message.message)
    if label == 0:
        return {
            "message": "Message is not relevant for memory."
        }

    message.label = label
    match label:
        case 1:
            store_short_term_memory(
                message.to_dict()
            ).delay()
        case 2:
            store_long_term_memory(
                message.to_dict()
            ).delay()
    return {
        "message": "Message stored in memory."
    }


@shared_task
def store_short_term_memory(message_dict: dict):
    message = Message(**message_dict)
    memory = ShortTermMemory(
        memory=message.message,
        user_id=message.user_id,
        access_count=0,
        merge_count=0,
        merged=False,
        created_at=datetime.new(),
        active=True
    )
    ShortTermMemoryRepository().create(memory)


@shared_task
def store_long_term_memory(message_dict: dict):
    message = Message(**message_dict)
    memory = LongTermMemory(
        memory=message.message,
        user_id=message.user_id,
        access_count=0,
        created_at=datetime.now(),
        active=True
    )
    LongTermMemoryRepository().create(memory)
