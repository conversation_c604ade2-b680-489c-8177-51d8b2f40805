from odmantic import AIOEngine

from src.models import User
from src.repositories.user import UserRepository


class UserService:
    def __init__(self):
        pass

    async def create_user(
        self, external_id: str, engine: AIOEngine
    ) -> str:
        user_model = User(external_id=external_id)
        user = await UserRepository().create_user(
            user_model, engine
        )
        return user

    async def get_users(self, engine: AIOEngine) -> list[User]:
        users = await UserRepository().get_users(engine)
        return users
