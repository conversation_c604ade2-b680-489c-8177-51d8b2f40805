from src.workers.memory_classification import process_message


class MessageService:
    def __init__(self):
        pass

    def process_message(self, user_id: str, message: str):
        """
        Processes a message for the user.

        Args:
            user_id (str): The user's ID.
            message (str): The message to be processed.
        """
        process_message({
            'user_id': user_id,
            'message': message
        }).delay()
