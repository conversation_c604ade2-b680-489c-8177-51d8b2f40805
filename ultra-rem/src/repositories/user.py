from odmantic import AIOEngine

from src.database.models import User as UserDB
from src.models import User as UserModel


class UserRepository:
    async def create_user(
        self, user: UserModel, engine: AIOEngine
    ) -> UserModel:
        user_db = UserDB(
            **user.to_dict()
        )
        print(user_db)
        await engine.save(user_db)
        return user

    async def get_users(
        self, engine: AIOEngine
    ) -> list[UserModel]:
        users_db = await engine.find(UserDB)
        return [UserModel(**user.__dict__) for user in users_db]
