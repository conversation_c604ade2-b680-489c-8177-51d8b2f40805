from odmantic import AIOEngine, SyncEngine

from src.database.models import LongTermMemory as LongTermMemoryDB
from src.models import LongTermMemory as LongTermMemoryModel


class LongTermMemoryRepository:
    async def create(
        self, memory: LongTermMemoryModel, engine: AIOEngine
    ) -> LongTermMemoryModel:
        memory_db = LongTermMemoryDB(
            **memory.to_dict()
        )
        await engine.save(memory_db)
        return memory

    def create_sync(
        self, memory: LongTermMemoryModel, engine: SyncEngine
    ) -> LongTermMemoryModel:
        memory_db = LongTermMemoryDB(
            **memory.to_dict()
        )
        engine.save(memory_db)
        return memory
