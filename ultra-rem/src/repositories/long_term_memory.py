from odmantic import AIOEngine

from src.database.models import LongTermMemory as LongTermMemoryDB
from src.models import LongTermMemory as LongTermMemoryModel


class LongTermMemoryRepository:
    async def create(
        self, memory: LongTermMemoryModel, engine: AIOEngine
    ) -> LongTermMemoryModel:
        memory_db = LongTermMemoryDB(
            **memory.dict()
        )
        await engine.save(memory_db)
        return memory
