from odmantic import AIOEngine

from src.database.models import ShortTermMemory as ShortTermMemoryDB
from src.models import ShortTermMemory as ShortTermMemoryModel


class ShortTermMemoryRepository:
    async def create(
        self, memory: ShortTermMemoryModel, engine: AIOEngine
    ) -> ShortTermMemoryModel:
        memory_db = ShortTermMemoryDB(
            **memory.dict()
        )
        await engine.save(memory_db)
        return memory
