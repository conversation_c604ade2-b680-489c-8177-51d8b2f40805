from odmantic import AIOEngine, SyncEngine

from src.database.models import ShortTermMemory as ShortTermMemoryDB
from src.models import ShortTermMemory as ShortTermMemoryModel


class ShortTermMemoryRepository:
    async def create(
        self, memory: ShortTermMemoryModel, engine: AIOEngine
    ) -> ShortTermMemoryModel:
        memory_db = ShortTermMemoryDB(
            **memory.to_dict()
        )
        await engine.save(memory_db)
        return memory

    def create_sync(
        self, memory: ShortTermMemoryModel, engine: SyncEngine
    ) -> ShortTermMemoryModel:
        memory_db = ShortTermMemoryDB(
            **memory.to_dict()
        )
        engine.save(memory_db)
        return memory
