from typing import Callable

from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import HTTPException
# from auth import authentication_middleware 
from .config import ApiConfig

PUBLIC_ROUTES = ApiConfig().public_routes


def add_cors_middleware(app):
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

#
# def add_authtentication_middleware(app):
#     app.middleware("http")(authtentication_middleware)
