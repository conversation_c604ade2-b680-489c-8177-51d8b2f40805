# from typing import Callable
#
# import jwt
# from fastapi import Request, Response, HTTPException
# from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
# from starlette.middleware.base import BaseHTTPMiddleware
#
# from config import ApiConfig
#
#
# class JWTBearer(HTTPBearer):
#     def __init__(self, auto_error: bool = True):
#         super(J<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(auto_error=auto_error)
#         self.config = ApiConfig()
#         self.public_routes = self.config.public_routes
#         self.secret_key = self.config.secret_key
#         self.algorithm = self.config.algorithm
#         self.token_prefix = self.config.token_prefix
#         self.token_expiration = self.config.token_expiration
#         self.token_issuer = self.config.token_issuer
#         self.token_audience = self.config.token_audience
#         self.token_subject = self.config.token_subject
#         self.token_leeway = self.config.token_leeway
#         self.token_lifetime = self.config.token_lifetime
#
#     def verify_jwt(self, jwtoken: str) -> bool:
#         try:
#             payload = jwt.decode(
#                 jwtoken,
#                 self.secret_key,
#                 algorithms=[self.algorithm],
#                 audience=self.token_audience,
#                 issuer=self.token_issuer,
#                 leeway=self.token_leeway,
#             )
#         except jwt.ExpiredSignatureError:
#             raise HTTPException(status_code=401, detail="Token expired")
#         except jwt.InvalidTokenError:
#             raise HTTPException(status_code=401, detail="Invalid token")
#         return True
#
#     async def __call__(self, request: Request) -> None:
#         credentials: HTTPAuthorizationCredentials = await super(
#             JWTBearer, self
#         ).__call__(request)
#         if credentials:
#             if not credentials.scheme == "Bearer":
#                 raise HTTPException(
#                     status_code=403, detail="Invalid authentication scheme."
#                 )
#             if not self.verify_jwt(credentials.credentials):
#                 raise HTTPException(
#                     status_code=403, detail="Invalid token or expired token.")
#             return credentials.credentials
#         else:
#             raise HTTPException(
#                 status_code=403, detail="Invalid authorization code.")
#
#     async def authenticate(self, request: Request) -> bool:
#         try:
#             await self.__call__(request)
#             return True
#         except HTTPException:
#             return False
#
#
# class JWTMiddleware(BaseHTTPMiddleware):
#     def __init__(self, app, authenticate: Callable[[Request], bool]):
#         super().__init__(app)
#         self.authenticate = authenticate
#         self.public_routes = self.config.public_routes
#         self.secret_key = self.config.secret_key
#         self.algorithm = self.config.algorithm
#         self.token_prefix = self.config.token_prefix
#         self.token_expiration = self.config.token_expiration
#         self.token_issuer = self.config.token_issuer
#         self.token_audience = self.config.token_audience
#         self.token_subject = self.config.token_subject
#         self.token_leeway = self.config.token_leeway
#
#     async def dispatch(self, request: Request, call_next: Callable) -> Response:
#         if await self.authenticate(request):
#             return await call_next(request)
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")
