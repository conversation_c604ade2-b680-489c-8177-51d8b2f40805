from fastapi import APIRouter
from fastapi.responses import JSONResponse

from src.services.message import MessageService
from .schemas.message import Message


router = APIRouter()


@router.put("/user/{user_id}")
async def add_message(message: Message) -> JSONResponse:
    """
    Enqueue a message to be processed for memory classification.
    """
    MessageService().process_message(
        message.user_id, message.message
    )
    return JSONResponse(
        status_code=200, content={"message": "Message added to queue"}
    )

