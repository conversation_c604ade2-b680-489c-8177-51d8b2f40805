from typing import List

from fastapi import APIRouter, Depends

from src.api.database import get_db
from src.api.v1.schemas.user import User
from src.services.user import UserService

router = APIRouter()


@router.post("", response_model=User)
async def create_user(user: User, db=Depends(get_db)):
    """Create a new user"""
    return await UserService().create_user(
        user.external_id, db
    )


@router.get("", response_model=List[User])
async def get_users(db=Depends(get_db)):
    """Get all users"""
    return await UserService().get_users(db)
