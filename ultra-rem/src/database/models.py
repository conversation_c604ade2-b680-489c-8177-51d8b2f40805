from datetime import datetime

from odmantic import Model, Field
from bson import ObjectId
from typing import ClassVar


class User(Model):
    external_id: str = Field(
        primary_field=True, unique=True
    )

    model_config: ClassVar = {
        "collection": "users"
    }


class LongTermMemory(Model):
    """
    Long term memory model
    """
    memory: str
    user_id: str
    access_count: int = Field(default=0)
    created_at: datetime
    active: bool = Field(default=True)

    model_config: ClassVar = {
        "collection": "long_term_memory",
        "indexes": [
            "user_id",
            [("user_id", 1), ("created_at", -1)]
        ]
    }


class ShortTermMemory(Model):
    """
    Short term memory model
    """
    memory: str
    user_id: str
    access_count: int = Field(default=0)
    merge_count: int = Field(default=0)
    merged: bool = Field(default=False)
    created_at: datetime
    active: bool = Field(default=True)

    model_config: ClassVar = {
        "collection": "short_term_memory",
        "indexes": [
            "user_id",
            [("user_id", 1), ("created_at", -1)]
        ]
    }
