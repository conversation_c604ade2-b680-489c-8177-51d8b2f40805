from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from pymongo import MongoClient
from odmantic import AI<PERSON>ngine, SyncEngine

from src.database.config import MongoDBConfig

CONFIG = MongoDBConfig()


class MongoDB:
    def __init__(self):
        self.host = CONFIG.host
        self.port = CONFIG.port
        self.database = CONFIG.db
        self.username = CONFIG.username
        self.password = CONFIG.password
        self.client = None
        self.db = None
        self.engine = None
        self.aio_client = None
        self.aio_db = None
        self.aio_engine = None

    def connect(self):
        self.client = MongoClient(
            self.host,
            self.port,
            username=self.username,
            password=self.password,
        )
        self.db = self.client[self.database]
        self.engine = SyncEngine(self.client, self.database)
        return self

    async def connect_async(self):
        self.aio_client = AsyncIOMotorClient(
            self.host,
            self.port,
            username=self.username,
            password=self.password,
        )
        self.aio_db = self.aio_client[self.database]
        self.aio_engine = AIOEngine(self.aio_client, self.database)
        return self

    def close(self):
        if self.client is None:
            return self
        self.client.close()
        return self

    async def close_async(self):
        if self.aio_client is None:
            return self
        self.aio_client.close()
        return self

    def __enter__(self) -> SyncEngine:
        self.connect()
        return self.engine

    async def __aenter__(self) -> AIOEngine:
        await self.connect_async()
        return self.aio_engine

    def __exit__(self, exc_type, exc_value, traceback):
        self.close()

    async def __aexit__(self, exc_type, exc_value, traceback):
        await self.close_async()
